interactions:
  - request:
      body:
        '{"input":"What''s 12 + 12?","model":"gpt-4o-mini","instructions":"Just
        the number please","temperature":0.5}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "107"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA3RTW27jMAz8zykEfTeFrdixkyPsFYqFwUh0oq0sGRJVtChy94XlR+zd9MewhuRo
          NCS/d4xxrfiZcY+hb471oZJtWxanOqvzUw3tsbyU2aWSQrUqLzNxUPmpEjliJSpx4i8Dgbv8QUkz
          ibMBR1x6BELVwBDLq6Iu6jqv8xQLBBTDUCNd1xskVGPRBeT71btoB1UtmIAJRu+d52dmozEJ0HYu
          bBQSaBO20UA+StLOpkt+xUCMbshs7C7oWW8QZpkdfDYuUh+pIfeOdkPUOYVmYLj2tC/cvtNW70Um
          in1W7fN6MiBV8zN72zHG2Hf6Ls524boYKxXUg7GnUogcqmNel1gccvXU2MRBXz0mFgwBrvgI/ORg
          CkpnCe1D0lrWhnZ+OH7SUp0SwFpHMBv49nsTTOlnxkXBF/g+/S2Z3DuT7oAQdCCwNCYPiSmJ9+DB
          GDQNOWcaCSY1kXwce957/NAuhmYeqyYZuvTGIwRntb3y8/Q4jm3rPK2SBqNi14H/msAdY/dxAtF/
          aIkNaRwGiytsIZrRBR7IeVxrIex69EAxwdlrOaHJh+ny1vkOHueVyylvefx4//jmm9NyNCmS40vg
          4Tkn1zf9sECv2Xj20crUmKRaB7iYeXliGpFFkLaboRbi5X98tT2LbAnyhupRmI3Sp+p/d0U8w5/R
          Lv36iZkcgVkRF4tZMeBm+zskUEAw0N93978AAAD//wMAT6oOaMgEAAA=
      headers:
        CF-RAY:
          - 9472cb5c9a194396-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:41 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=hky4PzuwZI0YHDeLe3UyB0OkeHxklBi6UeyYqib7OLc-1748488181-*******-.8FXGe8MaMHhsboFbj_N_g2.OEI.83OjQL9De3dC2FClH.g2WHfq1pgLdORTu5xsjVMjtC8bkH8EggkP45z2HxbiKAeTLML8I1CsyTnv30s;
            path=/; expires=Thu, 29-May-25 03:39:41 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=gQ7ZU3ok.ksIvbOQBFTWcOCVWLNwRjYQ.X9DzjPR8XU-1748488181926-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "603"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999959"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_972488ab47f149e1d2791593ecf33dc9
      status:
        code: 200
        message: OK
version: 1
