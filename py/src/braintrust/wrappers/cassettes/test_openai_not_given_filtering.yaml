interactions:
  - request:
      body: '{"messages":[{"role":"user","content":"What''s 12 + 12?"}],"model":"gpt-4o-mini","temperature":0.5}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "98"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//jFJNj9MwEL3nV1hzpVk12aBNewQkbgiJG2gVTe1p6q1ju/aEZVn1vyM7
          pcnCInHJYd5H3hvPcyEEaAVbAfKALAdvynfy0+P73dPxw+MG3ekrn35WY/v5qB42Xz5GWCWF2z2Q
          5N+qG+kGb4i1sxMsAyFTcq3umrZp26pdZ2BwikyS9Z7LxpWDtrqs13VTru/Kqr2oD05LirAV3woh
          hHjO35TTKvoBW5G98mSgGLEn2F5JQkBwJk0AY9SR0TKsZlA6y2Rz9KoWb0RVCzqNaKKom5slMdB+
          jJjC2tGYBYDWOsZUNke8vyDnayjjeh/cLv4hhb22Oh66QBidTQEiOw8ZPRdC3Ofy44s+4IMbPHfs
          jpR/VzWTHcwrn8H2grFjNPO4rlevmHWKGLWJi92BRHkgNSvnReOotFsAxaLy31le855qa9v/j/0M
          SEmeSXU+kNLyZd+ZFijd479o1xXnwBApfNeSOtYU0jMo2uNopiuB+BSZhm6vbU/BBz2dyt53tw2+
          bZA2txKKc/ELAAD//wMAaAEkkTgDAAA=
      headers:
        CF-RAY:
          - 9472cb57fe48659d-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:41 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=Yi6aaLaw0NZkdHvrki4WOE5tE6U94TABT1plkmJKfqQ-1748488181-*******-LdRP3crB0p5QFhTA5pwITOIrJYA7eAyD9jWRNne6ofo.KlYxvbAHDHsub1yEy5RdsLo_SoAYmKEddFZv2adJjFbeq3.3hc1sdtnjLcs_J.E;
            path=/; expires=Thu, 29-May-25 03:39:41 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=MoVXRPgslfEHsJs5qWj1Rd2oCC7HIsq2nTprVQqpfSQ-1748488181133-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "543"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "549"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_5fe641e45a5e8b15e3ad21c8469ecaf5
      status:
        code: 200
        message: OK
version: 1
