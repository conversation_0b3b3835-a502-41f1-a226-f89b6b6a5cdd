interactions:
  - request:
      body: '{"messages":[{"role":"user","content":"What is 5 + 5?"}],"model":"gpt-4o-mini"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "79"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//jJJBb9swDIXv/hUCr4uLOLERN8deCgzYEAzYaSgMVaIdrrKkSnS3osh/
          H2Snsdt1wC4+8OOj3qP5kgkBpGEvQB0lq96b/EZ9/XVzwMNnOujqkb+HuvgW6PbL5rB+eoZVUrj7
          n6j4VXWlXO8NMjk7YRVQMqapxa6sy7oudtcj6J1Gk2Sd57x0eU+W8s16U+brXV7UZ/XRkcIIe/Ej
          E0KIl/GbfFqNv2Ev1qvXSo8xyg5hf2kSAoIzqQIyRoosLcNqhspZRjtar8QnUQl8HKSJolhfLdsC
          tkOUyaodjFkAaa1jmaKOBu/O5HSxZFzng7uP76TQkqV4bALK6Gx6PrLzMNJTJsTdGH14kwZ8cL3n
          ht0Djs8V1TQO5oXPsD4zdizNXN5sVx8MazSyJBMXmwMl1RH1rJzXLAdNbgGyReS/vXw0e4pNtvuf
          8TNQCj2jbnxATept3rktYLrGf7VdVjwahojhiRQ2TBjSb9DYysFMNwLxOTL2TUu2w+ADTYfS+mZb
          yqqUeL1VkJ2yPwAAAP//AwAbCHd7NgMAAA==
      headers:
        CF-RAY:
          - 9472cb4d79f27277-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:39 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=l7mbGZ3Egstfh3PVdZpsOkqxw0vgJZvR00yYcXnuUj0-1748488179-*******-zGkugr7nCARwHeGFg9XPlc7ChjTjH6Av35tx.ewtqIg7hpn2SeuueDeV0sMixwIHsE7zECshDr4vaV2fXQpOBETRL98mLwHcpljBSJOCszo;
            path=/; expires=Thu, 29-May-25 03:39:39 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=wRUFkIxH7r5aq8YviVboE1R1m74GbxwHWRDWa9j1tGU-1748488179468-*******-*********;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "419"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "424"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "*********"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_c9e62f050322730d68d7df78f9b4144e
      status:
        code: 200
        message: OK
  - request:
      body: '{"messages":[{"role":"user","content":"What is 4 + 4?"}],"model":"gpt-4o-mini"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "79"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//jJJBb5wwEIXv/Aprrl0iIFR49xj1VimRkp5aRcixB9apsb320LSK9r9X
          hs1C0lTKhcN888bvDfOcMQZawY6B3AuSgzf5lbx+utpu+beb74fbr93BdKi4qitBX+6eYJMU7uER
          Jb2oLqQbvEHSzs5YBhSEaWrZ1LzmvGy2ExicQpNkvae8dvmgrc6roqrzoslLflLvnZYYYcd+ZIwx
          9jx9k0+r8DfsWLF5qQwYo+gRducmxiA4kyogYtSRhCXYLFA6S2gn6zX7xGqGh1GYyPjFuitgN0aR
          nNrRmBUQ1joSKenk7/5EjmdHxvU+uIf4Rgqdtjru24AiOptej+Q8TPSYMXY/JR9fhQEf3OCpJfcT
          p+fKz/M4WPa9QH5i5EiYpVxdbt4Z1iokoU1cLQ6kkHtUi3LZshiVdiuQrSL/6+W92XNsbfuPjF+A
          lOgJVesDKi1f513aAqZj/F/becWTYYgYfmmJLWkM6Tco7MRo5hOB+CcSDm2nbY/BBz3fSefbbYXF
          ZdMUvITsmP0FAAD//wMAHCTDkDUDAAA=
      headers:
        CF-RAY:
          - 9472cb4d795e5f74-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:39 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=D5wymTQyeow3dD83thuE0eWShdgxY38D0kJXHd.ZMUs-1748488179-*******-3bJsQ2EFtzxrDG1vWH33upwDiXRf3MLCgHZtvF2S2Z8eeqnEXgubmRS67THCTKIsxmdeS7xfGAclinCKqjvVrqhIqzYV9maCn8wdpz.gn7s;
            path=/; expires=Thu, 29-May-25 03:39:39 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=62gNDFO6c1qtFW93mdU_wkFNdaV989TC7j.Q3x9737c-1748488179939-*******-*********;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "940"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "944"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "*********"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_18867c173fff6d22299475f3b28f4309
      status:
        code: 200
        message: OK
  - request:
      body: '{"messages":[{"role":"user","content":"What is 3 + 3?"}],"model":"gpt-4o-mini"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "79"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//jJJNb9QwEIbv+RXWXNlUmw+66R4LSIVDhZAQB1RFU2eSGBzba0/4qva/
          IyfbTQpF4pLDPPOO33cyD4kQoBrYC5A9shycTq/l7fdrf3ODH37dvj8Ur/r808c3r4fDu7flYYRN
          VNj7LyT5UXUh7eA0sbJmxtITMsWp2a6syqrKdlcTGGxDOso6x2lp00EZlebbvEy3uzSrTureKkkB
          9uJzIoQQD9M3+jQN/YC92G4eKwOFgB3B/twkBHirYwUwBBUYDcNmgdIaJjNZL8QLUQg6jKiDuLxY
          d3lqx4DRqRm1XgE0xjLGpJO/uxM5nh1p2zlv78MfUmiVUaGvPWGwJr4e2DqY6DER4m5KPj4JA87b
          wXHN9itNz2Uv53Gw7HuB1YmxZdRLOS82zwyrG2JUOqwWBxJlT82iXLaMY6PsCiSryH97eW72HFuZ
          7n/GL0BKckxN7Tw1Sj7Nu7R5isf4r7bziifDEMh/U5JqVuTjb2ioxVHPJwLhZ2Aa6laZjrzzar6T
          1tWXOeYFVhm1kByT3wAAAP//AwCBHV+XNQMAAA==
      headers:
        CF-RAY:
          - 9472cb4d78470cba-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:40 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=EulnPVDmBHvrBtVkP1Jb.FV9nLpFLLHSlsHyw3.ExiY-1748488180-*******-ATZmk8OiTX7FxVt2AmMY6VbrIgBwJDUz.dZ3VuyP9F6AjARSsy1P35lGfqBnpwotplfQSvSeQGravGggG2YZCr9jsTkOHPjEPqtFv2XzaEQ;
            path=/; expires=Thu, 29-May-25 03:39:40 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=yZeym3GpRkVaB2a4ERNO5W9Sg71J9kZgzB6.k9C_7Hc-1748488180353-*******-*********;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "1267"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "1272"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "*********"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_fa5dfb6ce3a2637a5f9803efbf54bef0
      status:
        code: 200
        message: OK
version: 1
