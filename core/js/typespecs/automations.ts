import { z } from "zod";

const webhookAutomationActionSchema = z.object({
  type: z.literal("webhook").describe("The type of action to take"),
  url: z.string().describe("The webhook URL to send the request to"),
});

export const automationEventTypeEnum = z.enum(["logs", "btql_export"]);
const intervalSecondsSchema = z
  .number()
  .min(1)
  .max(30 * 24 * 60 * 60)
  .describe(
    "Perform the triggered action at most once in this interval of seconds",
  );
export const logAutomationConfigSchema = z.object({
  event_type: z.literal("logs").describe("The type of automation."),
  btql_filter: z
    .string()
    .describe("BTQL filter to identify rows for the automation rule"),
  interval_seconds: intervalSecondsSchema,
  action: z
    .discriminatedUnion("type", [webhookAutomationActionSchema])
    .describe("The action to take when the automation rule is triggered"),
});

export const exportDefinitionTypeEnum = z.enum([
  "log_traces",
  "log_spans",
  "btql_query",
]);

export const exportDefinitionSchema = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("log_traces"),
  }),
  z.object({
    type: z.literal("log_spans"),
  }),
  z.object({
    type: z.literal("btql_query"),
    btql_query: z.string().describe("The BTQL query to export"),
  }),
]);
export type ExportDefinition = z.infer<typeof exportDefinitionSchema>;

const exportCredentialsSchema = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("aws_iam"),
    role_arn: z.string().describe("The ARN of the IAM role to use"),
    external_id: z
      .string()
      .describe(
        "The automation-specific external id component (auto-generated by default)",
      ),
  }),
]);

export const btqlExportAutomationConfigSchema = z.object({
  event_type: z.literal("btql_export").describe("The type of automation."),
  export_definition: exportDefinitionSchema.describe(
    "The definition of what to export",
  ),
  export_path: z
    .string()
    .describe(
      "The path to export the results to. It should include the storage protocol and prefix, e.g. s3://bucket-name/path/to/export",
    ),
  format: z
    .enum(["jsonl", "parquet"])
    .describe("The format to export the results in"),
  interval_seconds: intervalSecondsSchema,
  credentials: exportCredentialsSchema,
  batch_size: z
    .number()
    .nullish()
    .describe("The number of rows to export in each batch"),
});

export const automationConfigSchema = z.union([
  logAutomationConfigSchema,
  btqlExportAutomationConfigSchema,
]);
export type AutomationConfig = z.infer<typeof automationConfigSchema>;
