{"name": "@braintrust/core", "version": "0.0.90", "description": "Shared core dependencies for Braintrust packages", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "watch": "tsup --watch", "clean": "rm -r dist/*", "prepublishOnly": "../../../scripts/node_prepublish_core.py", "test": "vitest run"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "module": "./dist/index.mjs", "require": "./dist/index.js"}, "./typespecs": {"types": "./typespecs/dist/index.d.ts", "import": "./typespecs/dist/index.mjs", "module": "./typespecs/dist/index.mjs", "require": "./typespecs/dist/index.js"}, "./typespecs-stainless": {"types": "./typespecs-stainless/dist/index.d.ts", "import": "./typespecs-stainless/dist/index.mjs", "module": "./typespecs-stainless/dist/index.mjs", "require": "./typespecs-stainless/dist/index.js"}}, "files": ["dist/**/*", "typespecs/dist/**/*", "typespecs-stainless/dist/**/*"], "license": "MIT", "publishConfig": {"access": "public"}, "homepage": "https://www.braintrust.dev", "repository": {"type": "git", "url": "git+https://github.com/braintrustdata/braintrust-sdk.git"}, "bugs": {"url": "https://github.com/braintrustdata/braintrust-sdk/issues"}, "keywords": ["ai"], "devDependencies": {"@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "tsup": "^8.3.5", "tsx": "^3.14.0", "typescript": "^5.3.3", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.1.9"}, "dependencies": {"@asteasolutions/zod-to-openapi": "^6.3.1", "uuid": "^9.0.1", "zod": "^3.25.34"}}